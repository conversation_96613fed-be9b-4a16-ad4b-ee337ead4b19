'use client';

import { api } from './api';

// 前端 LangGraph 客户端服务
export class LangGraphClient {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/langgraph') {
    this.baseUrl = baseUrl;
  }

  // 发送聊天消息
  async sendMessage(message: string, threadId?: string): Promise<any> {
    const response = await api.post(`${this.baseUrl}/chat`, { message, threadId });
    return response.data;
  }

  // 分析股票
  async analyzeStock(ticker: string, config: any = {}, threadId?: string): Promise<any> {
    const response = await api.post(`${this.baseUrl}/analyze`, { ticker, config, threadId });
    return response.data;
  }

  // 流式分析
  async *streamAnalysis(ticker: string, config: any = {}, threadId?: string): AsyncGenerator<any> {
    const response = await fetch(`${this.baseUrl}/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ticker, config, threadId }),
    });

    if (!response.ok) {
      throw new Error(`流式分析请求失败: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.error) {
                throw new Error(data.error);
              }
              yield data;
            } catch (parseError) {
              console.error('解析流数据失败:', parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // 获取会话状态
  async getSessionState(threadId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/state?threadId=${threadId}`);

    if (!response.ok) {
      if (response.status === 404) {
        return null; // 会话不存在
      }
      throw new Error(`获取状态失败: ${response.statusText}`);
    }

    return await response.json();
  }

  // 清除会话
  async clearSession(threadId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/state`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ threadId }),
    });

    if (!response.ok) {
      throw new Error(`清除会话失败: ${response.statusText}`);
    }
  }

  // 删除会话
  async deleteSession(threadId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/state?threadId=${threadId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`删除会话失败: ${response.statusText}`);
    }
  }

  // 创建 WebSocket 连接信息
  async getWebSocketInfo(threadId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/websocket?threadId=${threadId}`);

    if (!response.ok) {
      throw new Error(`获取 WebSocket 信息失败: ${response.statusText}`);
    }

    return await response.json();
  }
}

// 创建全局客户端实例
export const langGraphClient = new LangGraphClient();

// 导出类型定义
export interface ChatMessage {
  id: string;
  type: 'human' | 'ai' | 'tool' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface SessionState {
  threadId: string;
  messages: any[];
  currentStep: string;
  isProcessing: boolean;
  analysisResults?: any;
  tradingDecision?: any;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AnalysisConfig {
  analysisType?: 'basic' | 'comprehensive' | 'quick';
  includeRisk?: boolean;
  includeSentiment?: boolean;
  includeNews?: boolean;
  timeframe?: '1d' | '1w' | '1m' | '3m' | '1y';
  [key: string]: any;
}
